# 迭代算法

## 概念
迭代算法是一种通过重复执行一系列操作来解决问题的算法。它通常涉及使用循环结构（如 for 循环或 while 循环）来逐步逼近最终结果或处理数据集中的每个元素。在每次迭代中，算法会更新一个或多个变量的状态，直到满足某个终止条件。

## 特点
- **状态更新**：迭代算法的核心在于循环过程中状态变量的持续更新。
- **终止条件**：必须有一个明确的终止条件，以防止无限循环。
- **逐步逼近**：通过一系列重复的步骤，从初始状态逐步达到最终解。
- **显式控制**：迭代过程由程序员显式控制循环的初始化、条件判断和状态更新。

## 应用场景
迭代算法广泛应用于各种计算问题，例如：
- **数值计算**：如求解方程的近似根（牛顿迭代法）、数值积分。
- **数据处理**：遍历数组、列表、集合等数据结构中的元素进行处理。
- **搜索算法**：如顺序查找。
- **排序算法**：如冒泡排序、选择排序、插入排序等，其核心操作通常是迭代的。
- **动态规划**：某些动态规划问题的解法也依赖于迭代计算。

## 与递归算法的比较
| 特性         | 迭代算法                                 | 递归算法                                     |
|--------------|------------------------------------------|----------------------------------------------|
| **基本思想**   | 通过循环重复执行代码块                     | 函数调用自身来解决更小规模的子问题             |
| **状态管理**   | 通常使用局部变量显式管理状态               | 通过函数调用栈隐式管理状态                   |
| **终止条件**   | 循环的终止条件                           | 递归的基线条件 (Base Case)                   |
| **代码结构**   | 通常更长，但可能更直接易懂（对于某些问题） | 通常更简洁，更接近数学定义（对于某些问题）     |
| **性能开销**   | 函数调用开销较小，通常效率较高             | 函数调用开销较大（栈帧创建和销毁），可能导致栈溢出 |
| **适用性**     | 适合解决那些可以被分解为重复步骤的问题     | 适合解决那些具有自相似结构的问题             |

选择迭代还是递归，通常取决于问题的性质、代码的清晰度以及性能要求。许多递归算法都可以用迭代的方式实现，反之亦然，但转换的复杂性会有所不同。
