# 算法基础概念与效率

## 什么是算法？

- **定义**：算法（Algorithm）是一系列清晰定义的、可执行的指令，用于解决特定类型的问题或完成特定任务。它描述了解决问题所需的步骤和顺序。
- **特性**：一个有效的算法通常具备以下五个基本特性：
    1.  **有穷性 (Finiteness)**：算法必须在执行有限的步骤后终止，每个步骤也必须在有限的时间内完成。
    2.  **确定性 (Definiteness)**：算法的每一步都必须有确切的含义，对于相同的输入，算法的执行路径和输出必须是唯一的，不能有歧义。
    3.  **可行性 (Effectiveness/Feasibility)**：算法中的每一步操作都必须是足够基本的，可以通过已经实现的基本运算执行有限次来实现。也就是说，算法的每一步都是可以执行的。
    4.  **输入 (Input)**：一个算法有零个或多个输入。这些输入是算法开始前给予算法的量，取自于某个特定的对象的集合。
    5.  **输出 (Output)**：一个算法有一个或多个输出。这些输出是与输入有特定关系的量，是算法进行信息处理后得到的结果。

## 算法设计的要求
设计一个好的算法通常需要考虑以下几个方面：

- **正确性 (Correctness)**：算法应当能够对于合法的输入数据产生满足要求的结果。这是算法最重要的基本要求。通常正确性包含以下四个层次：
    1.  程序不含语法错误。
    2.  程序对于几组典型的输入数据能够得出满足规格说明要求的结果。
    3.  程序对于精心选择的、带有刁难性的几组输入数据能够得出满足规格说明要求的结果。
    4.  程序对于一切合法的输入数据都能得出满足规格说明要求的结果。
- **可读性 (Readability)**：算法应当易于人们阅读、理解和交流。清晰的算法有助于调试、修改和维护。
- **健壮性 (Robustness)**：当输入非法数据时，算法应能适当地做出反应或进行处理，而不会产生不可预料的输出结果，或者直接崩溃。例如，给出错误提示信息。
- **高效率与低存储量需求 (Efficiency and Low Storage Requirement)**：算法执行时间应尽可能短（时间效率高），算法执行过程中所需的存储空间应尽可能少（空间效率高）。效率是评价算法好坏的一个重要标准。

## 算法效率的度量
算法效率主要从**时间复杂度**和**空间复杂度**两个方面进行度量。

### 时间复杂度 (Time Complexity)
- **定义**：算法的时间复杂度是一个函数，它定量描述了该算法的运行时间。它通常估算的是算法执行基本操作的次数，而不是实际的运行时间（因为实际运行时间受硬件、编程语言、编译器等多种因素影响）。我们关心的是当问题规模 n 趋向于无穷大时，执行次数的增长趋势。
- **大O表示法 (Big O Notation)**：用于描述算法时间复杂度的渐进上界。它表示当问题规模 n 增大时，算法执行时间的增长率。例如，如果一个算法的时间复杂度是 O(n²)，意味着执行时间与问题规模 n 的平方成正比。
- **常见时间复杂度**：按效率从高到低（增长率从小到大）排列：
    - **O(1) - 常数时间 (Constant Time)**：执行时间与输入规模 n 无关。例如，访问数组中的一个特定元素。
    - **O(log n) - 对数时间 (Logarithmic Time)**：执行时间随 n 的对数增长。例如，二分查找。
    - **O(n) - 线性时间 (Linear Time)**：执行时间与 n 成正比。例如，顺序查找一个列表。
    - **O(n log n) - 线性对数时间 (Linearithmic Time)**：执行时间是 n 乘以 log n。例如，高效的排序算法如归并排序、快速排序（平均情况）。
    - **O(n²) - 平方时间 (Quadratic Time)**：执行时间与 n 的平方成正比。例如，简单的排序算法如冒泡排序、选择排序、插入排序（在大多数情况下）。
    - **O(n³) - 立方时间 (Cubic Time)**：执行时间与 n 的立方成正比。例如，某些矩阵运算。
    - **O(2ⁿ) - 指数时间 (Exponential Time)**：执行时间以 2 的 n 次方增长。例如，某些暴力搜索算法，如求解旅行商问题的朴素解法。
    - **O(n!) - 阶乘时间 (Factorial Time)**：执行时间以 n 的阶乘增长。例如，生成n个元素的全排列。
- **最好情况、最坏情况、平均情况时间复杂度**：
    - **最坏情况时间复杂度 (Worst-case)**：算法在任何输入实例上运行时间的最大值。这是最重要的度量，因为它保证了算法运行时间的一个上界。
    - **最好情况时间复杂度 (Best-case)**：算法在任何输入实例上运行时间的最小值。
    - **平均情况时间复杂度 (Average-case)**：算法在所有可能输入实例上运行时间的期望值（通常假设输入是随机的）。

### 空间复杂度 (Space Complexity)
- **定义**：算法的空间复杂度是一个函数，它定量描述了该算法在运行过程中临时占用的存储空间大小。它包括程序代码所占用的空间、输入数据所占用的空间以及算法执行过程中额外需要的辅助空间。
- **大O表示法**：同样使用大O表示法来描述空间复杂度的渐进趋势。
- **常见空间复杂度**：
    - **O(1) - 常数空间**：算法所需的额外空间不随输入规模 n 的变化而变化。例如，顺序查找、冒泡排序（原地版本）。
    - **O(n) - 线性空间**：算法所需的额外空间与输入规模 n 成正比。例如，需要一个与输入同样大小的辅助数组。
    - **O(n²) - 平方空间**：算法所需的额外空间与输入规模 n 的平方成正比。
    - **O(log n) - 对数空间**：例如，递归算法如果递归深度是 log n，那么栈空间就是 O(log n)。

在分析算法时，通常更关注时间复杂度，但空间复杂度在内存受限的环境中也非常重要。理想的算法是既快又省空间的。
