---
title: 冒泡排序
---

# 冒泡排序

## 算法定义

冒泡排序（Bubble Sort）是一种简单的排序算法，它重复地遍历要排序的数列，一次比较两个元素，如果它们的顺序错误就把它们交换过来。遍历数列的工作是重复地进行直到没有再需要交换为止，此时该数列已经排序完成。这个算法的名字由来是因为越小的元素会经由交换慢慢"浮"到数列的顶端，就像水中的气泡最终会上浮到水面一样。

## 运算过程

冒泡排序的基本思想是：
1. 比较相邻的元素。如果第一个比第二个大（升序排列），就交换它们两个。
2. 对每一对相邻元素作同样的工作，从开始第一对到结尾的最后一对。这步做完后，最后的元素会是最大的数。
3. 针对所有的元素重复以上的步骤，除了最后一个。
4. 重复步骤1~3，直到没有任何一对数字需要比较。

## 示例Python程序

```python
def bubble_sort(arr):
    n = len(arr)  # 列表长度
    for i in range(n):  # 控制排序轮数
        swapped = False  # 标记本轮是否发生交换
        for j in range(0, n - i - 1):  # 相邻元素两两比较
            if arr[j] > arr[j + 1]:  # 如果顺序错误则交换
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
                swapped = True
        if not swapped:  # 若未发生交换说明已排好序
            break
    return arr

data = [64, 34, 25, 12, 22, 11, 90]
print(bubble_sort(data))
```

## 程序解释

1. **函数定义**：`bubble_sort`函数接收一个列表作为参数，并返回排序后的列表。

2. **外层循环**：通过`for i in range(n)`创建一个循环，最多执行n次（n为列表长度）。每一次外层循环完成后，当前最大的元素会被放到正确的位置。

3. **优化标志**：设置`swapped`变量作为优化标志。如果在某一轮比较中没有发生交换，说明列表已经有序，可以提前结束排序过程。

4. **内层循环**：通过`for j in range(0, n - i - 1)`创建内层循环，用于比较相邻元素。注意循环范围是`0`到`n - i - 1`，因为每完成一轮外层循环，最后的`i`个元素已经排好序了。

5. **元素比较与交换**：如果当前元素大于下一个元素，则交换它们的位置，并将`swapped`标志设为`True`。

6. **提前结束**：如果在某一轮比较中没有发生交换（`swapped`为`False`），说明列表已经有序，使用`break`提前结束排序。

7. **示例**：创建一个列表并调用`bubble_sort`函数输出排序结果。

冒泡排序的时间复杂度为O(n²)，其中n是列表的长度。虽然这个算法不是最高效的排序算法，但它概念简单，易于实现，适合用于教学目的和小规模数据的排序。
